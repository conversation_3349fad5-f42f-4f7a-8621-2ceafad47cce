import React, { useState } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Alert,
  FlatList,
  useWindowDimensions, // Import useWindowDimensions
} from 'react-native';
import {
  Target,
  AlertCircle,
  Flag,
  Clock,
  Circle,
  CheckCircle,
  PlayCircle,
  X,
} from 'lucide-react-native';
import {
  useSharedValue,
  withSpring,
  runOnJS,
} from 'react-native-reanimated';

import { useTasks } from '@/hooks/useTasks';
import { useTaskReminders } from '@/hooks/useTaskReminders';
import { useSubjects } from '@/hooks/useSubjects';
import { useTheme } from '@/contexts/ThemeContext';
import { Task } from '@/types/app';
import { goalsStyles } from '@/styles/goals.styles';
import { goalsExtendedStyles } from '@/styles/goals-extended.styles';

// Import components
import GoalsHeader from '@/components/goals/GoalsHeader';
import ProgressChart from '@/components/goals/ProgressChart';
import MilestoneProgress from '@/components/goals/MilestoneProgress';
import ProductivityAnalytics from '@/components/goals/ProductivityAnalytics';
import { TaskCard } from '@/components/goals/TaskCard';
import { TaskModal } from '@/components/goals/TaskModal';
import { FilterModal } from '@/components/goals/FilterModal';

const scale = (size: number, screenWidth: number) => (screenWidth / 375) * size;









export default function GoalsScreen() {
  const { theme } = useTheme();
  const {
    tasks,
    loading,
    createTask,
    updateTask,
    deleteTask,
    completeTask,
    updateTaskProgress,
  } = useTasks();

  const { getSubjectById } = useSubjects();

  // Task reminders and notifications
  const {
    scheduleTaskReminder,
    cancelTaskReminders,
    showTaskCompletionNotification,
    showProductivityMilestone,
  } = useTaskReminders({
    tasks,
    onTaskCompleted: (task) => {
      console.log('Task completed:', task.title);
    },
    onStreakAchieved: (streakCount, streakType) => {
      console.log(`${streakType} streak achieved:`, streakCount);
    },
  });

  // Modal states
  const [showAddModal, setShowAddModal] = useState(false);
  const [showFilterModal, setShowFilterModal] = useState(false);
  const [editingTask, setEditingTask] = useState<Task | null>(null);

  // Form states
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [dueDate, setDueDate] = useState<Date | undefined>(undefined);
  const [priority, setPriority] = useState<'low' | 'medium' | 'high' | 'urgent'>('medium');
  const [reminderEnabled, setReminderEnabled] = useState(false);
  const [reminderTime, setReminderTime] = useState<Date | undefined>(undefined);
  const [periodicReminders, setPeriodicReminders] = useState(false);
  const [reminderInterval, setReminderInterval] = useState<'daily' | 'weekly' | 'monthly'>('daily');
  const [selectedSubject, setSelectedSubject] = useState<any>(null);
  const [tags, setTags] = useState<string[]>([]);
  const [isMilestone, setIsMilestone] = useState(false);



  // Filter states
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedView, setSelectedView] = useState<'all' | 'todo' | 'in_progress' | 'completed'>('all');

  // UI states
  const [showStats, setShowStats] = useState(false);

  const modalScale = useSharedValue(0);
  const filterModalScale = useSharedValue(0);

  // Helper function for periodic reminders
  const schedulePeriodicReminders = async (task: Task, interval: 'daily' | 'weekly' | 'monthly') => {
    try {
      // This would integrate with your reminder system to schedule recurring notifications
      console.log(`Scheduling ${interval} reminders for task: ${task.title}`);
      // Implementation would depend on your notification system
    } catch (error) {
      console.error('Error scheduling periodic reminders:', error);
    }
  };

  // Reset form function
  const resetForm = () => {
    setTitle('');
    setDescription('');
    setDueDate(undefined);
    setPriority('medium');
    setSelectedSubject(null);
    setTags([]);
    setIsMilestone(false);
    setReminderEnabled(false);
    setReminderTime(undefined);
    setPeriodicReminders(false);
    setReminderInterval('daily');
  };

  const openAddModal = () => {
    setEditingTask(null);
    resetForm();
    setShowAddModal(true);
    modalScale.value = withSpring(1);
  };

  const openEditModal = (task: Task) => {
    setEditingTask(task);
    setTitle(task.title);
    setDescription(task.description || '');
    setDueDate(task.due_date || undefined);
    setPriority(task.priority);
    setSelectedSubject(task.subject_id ? getSubjectById(task.subject_id) : null);
    setTags(task.tags);
    setIsMilestone(task.is_milestone);
    setReminderEnabled(task.reminder_enabled || false);
    setReminderTime(task.reminder_time || undefined);
    setPeriodicReminders(false); // Default for existing tasks
    setReminderInterval('daily');
    setShowAddModal(true);
    modalScale.value = withSpring(1);
  };

  const closeModal = () => {
    modalScale.value = withSpring(0, {}, () => {
      runOnJS(setShowAddModal)(false);
    });
  };

  const openFilterModal = () => {
    setShowFilterModal(true);
    filterModalScale.value = withSpring(1);
  };

  const closeFilterModal = () => {
    filterModalScale.value = withSpring(0, {}, () => {
      runOnJS(setShowFilterModal)(false);
    });
  };

  const handleSave = async () => {
    if (!title.trim()) {
      Alert.alert('Error', 'Please enter a task title');
      return;
    }

    const taskData = {
      title: title.trim(),
      description: description.trim(),
      priority,
      due_date: dueDate,
      subject_id: selectedSubject?.id,
      tags,
      is_milestone: isMilestone,
      reminder_enabled: reminderEnabled,
      reminder_time: reminderEnabled && reminderTime ? reminderTime : undefined,
    };

    try {
      let savedTask;
      if (editingTask) {
        savedTask = await updateTask(editingTask.id, taskData);
      } else {
        savedTask = await createTask(taskData);
      }

      // Schedule reminders if enabled
      if (savedTask && reminderEnabled) {
        if (reminderTime) {
          await scheduleTaskReminder(savedTask, 'custom');

          // Schedule periodic reminders if enabled
          if (periodicReminders) {
            await schedulePeriodicReminders(savedTask, reminderInterval);
          }
        }
        if (savedTask.due_date) {
          await scheduleTaskReminder(savedTask, 'due_soon');
        }
        if (savedTask.is_milestone) {
          await scheduleTaskReminder(savedTask, 'milestone_due');
        }
      }

      closeModal();
    } catch (error) {
      console.error('Error saving task:', error);
      Alert.alert('Error', 'Failed to save task. Please try again.');
    }
  };

  const handleDelete = (task: Task) => {
    Alert.alert(
      'Delete Task',
      `Are you sure you want to delete "${task.title}"?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              await deleteTask(task.id);
            } catch (error) {
              Alert.alert('Error', 'Failed to delete task. Please try again.');
            }
          },
        },
      ]
    );
  };

  const handleToggleComplete = async (task: Task) => {
    try {
      if (task.status === 'completed') {
        // Uncomplete task
        await updateTask(task.id, {
          status: 'todo',
          progress_percentage: 0,
          completion_date: undefined
        });

        // Cancel completion notifications and reschedule reminders
        await cancelTaskReminders(task.id);
        if (task.reminder_enabled) {
          await scheduleTaskReminder(task, 'custom');
        }
      } else {
        // Complete task
        await completeTask(task.id);

        // Show completion notification
        await showTaskCompletionNotification(task);

        // Cancel any pending reminders for this task
        await cancelTaskReminders(task.id);

        // Check for productivity milestones
        const completedTasks = tasks.filter(t => t.status === 'completed').length + 1;
        if (completedTasks % 10 === 0) {
          await showProductivityMilestone(`You've completed ${completedTasks} tasks! Amazing progress!`);
        }
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to update task. Please try again.');
    }
  };

  // Utility functions
  const getPriorityColor = (priority: 'low' | 'medium' | 'high' | 'urgent') => {
    switch (priority) {
      case 'urgent': return '#DC2626';
      case 'high': return '#EF4444';
      case 'medium': return '#F59E0B';
      case 'low': return '#10B981';
      default: return '#6B7280';
    }
  };

  const getPriorityIcon = (priority: 'low' | 'medium' | 'high' | 'urgent') => {
    switch (priority) {
      case 'urgent': return <AlertCircle size={16} color="#DC2626" />;
      case 'high': return <Flag size={16} color="#EF4444" />;
      case 'medium': return <Clock size={16} color="#F59E0B" />;
      case 'low': return <Circle size={16} color="#10B981" />;
      default: return <Circle size={16} color="#6B7280" />;
    }
  };



  const getStatusIcon = (status: Task['status']) => {
    switch (status) {
      case 'completed': return <CheckCircle size={20} color="#10B981" />;
      case 'in_progress': return <PlayCircle size={20} color="#3B82F6" />;
      case 'cancelled': return <X size={20} color="#6B7280" />;
      default: return <Circle size={20} color="#F59E0B" />;
    }
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    });
  };

  const isOverdue = (date: Date) => {
    return date < new Date() && date.toDateString() !== new Date().toDateString();
  };



  // Filter and sort tasks
  const { width: screenWidth } = useWindowDimensions();

  const getFilteredTasks = () => {
    let filtered = tasks;

    // Apply view filter
    if (selectedView !== 'all') {
      filtered = filtered.filter(task => task.status === selectedView);
    }

    // Apply search filter
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(task =>
        task.title.toLowerCase().includes(query) ||
        task.description?.toLowerCase().includes(query) ||
        task.tags.some(tag => tag.toLowerCase().includes(query))
      );
    }

    return filtered.sort((a, b) => {
      // Sort by priority first (urgent > high > medium > low)
      const priorityOrder = { urgent: 4, high: 3, medium: 2, low: 1 };
      const priorityDiff = priorityOrder[b.priority] - priorityOrder[a.priority];
      if (priorityDiff !== 0) return priorityDiff;

      // Then by due date (closest first)
      if (a.due_date && b.due_date) {
        return a.due_date.getTime() - b.due_date.getTime();
      }
      if (a.due_date) return -1;
      if (b.due_date) return 1;

      // Finally by creation date (newest first)
      return b.created_at.getTime() - a.created_at.getTime();
    });
  };



  // Get filtered and organized data
  const filteredTasks = getFilteredTasks();

  // Create themed styles
  const styles = goalsStyles(theme);
  const extendedStyles = goalsExtendedStyles(theme);

  return (
    <View style={styles.container}>
      <GoalsHeader
        searchQuery={searchQuery}
        setSearchQuery={setSearchQuery}
        showStats={showStats}
        setShowStats={setShowStats}
        selectedView={selectedView}
        setSelectedView={setSelectedView}
        onAddTask={openAddModal}
        onOpenFilter={openFilterModal}
        tasks={tasks}
        screenWidth={screenWidth} // Pass screenWidth to GoalsHeader
      />

      {/* Enhanced Progress Visualization */}
      {showStats && (
        <ScrollView showsVerticalScrollIndicator={false}>
          <ProgressChart tasks={tasks} screenWidth={screenWidth} />
          <MilestoneProgress tasks={tasks} screenWidth={screenWidth} />
          <ProductivityAnalytics tasks={tasks} screenWidth={screenWidth} />
        </ScrollView>
      )}




      {/* Task List */}
      <ScrollView style={styles.taskListContainer} showsVerticalScrollIndicator={false}>
        {loading ? (
          <View style={styles.loadingContainer}>
            <Text style={styles.loadingText}>Loading tasks...</Text>
          </View>
        ) : filteredTasks.length === 0 ? (
          <View style={extendedStyles.emptyState}>
            <Target size={scale(48, screenWidth)} color={theme.colors.text.disabled} />
            <Text style={extendedStyles.emptyTitle}>No tasks found</Text>
            <Text style={extendedStyles.emptyText}>
              {searchQuery ? 'Try adjusting your search or filters' : 'Create your first task to get started!'}
            </Text>
          </View>
        ) : (
          <FlatList
            data={filteredTasks}
            keyExtractor={(item) => item.id}
            renderItem={({ item: task }) => (
              <TaskCard
                task={task}
                onToggleComplete={() => handleToggleComplete(task)}
                onEdit={() => openEditModal(task)}
                onDelete={() => handleDelete(task)}
                onUpdateProgress={(progress: number) => updateTaskProgress(task.id, progress)}
                getPriorityColor={getPriorityColor}
                getPriorityIcon={getPriorityIcon}
                getStatusIcon={getStatusIcon}
                formatDate={formatDate}
                isOverdue={isOverdue}
                getSubjectById={getSubjectById}
                screenWidth={screenWidth} // Pass screenWidth to TaskCard
              />
            )}
            scrollEnabled={false}
            showsVerticalScrollIndicator={false}
          />
        )}
      </ScrollView>

      <TaskModal
        visible={showAddModal}
        editingTask={editingTask}
        title={title}
        setTitle={setTitle}
        description={description}
        setDescription={setDescription}
        dueDate={dueDate}
        setDueDate={setDueDate}
        priority={priority}
        setPriority={setPriority}
        reminderEnabled={reminderEnabled}
        setReminderEnabled={setReminderEnabled}
        reminderTime={reminderTime}
        setReminderTime={setReminderTime}
        periodicReminders={periodicReminders}
        setPeriodicReminders={setPeriodicReminders}
        reminderInterval={reminderInterval}
        setReminderInterval={setReminderInterval}
        selectedSubject={selectedSubject}
        setSelectedSubject={setSelectedSubject}
        isMilestone={isMilestone}
        setIsMilestone={setIsMilestone}
        onClose={closeModal}
        onSave={handleSave}
        onDelete={editingTask ? () => handleDelete(editingTask) : undefined}
        getPriorityColor={getPriorityColor}
        getPriorityIcon={getPriorityIcon}
        modalScale={modalScale}
        screenWidth={screenWidth} // Pass screenWidth to TaskModal
      />


      <FilterModal
        visible={showFilterModal}
        selectedView={selectedView}
        setSelectedView={setSelectedView}
        onClose={closeFilterModal}
        filterModalScale={filterModalScale}
        screenWidth={screenWidth} // Pass screenWidth to FilterModal
      />
    </View>
  );
}
