import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Dimensions,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { MaterialIcons } from '@expo/vector-icons';
import { Analytics, MonthlyStat } from '@/types/app';

const { width } = Dimensions.get('window');

interface MonthlyTabProps {
  analytics: Analytics | null;
  formatTime: (seconds: number) => string;
  loading: boolean;
  error: string | null;
}

export function MonthlyTab({ analytics, formatTime, loading, error }: MonthlyTabProps) {
  
  const renderMonthlyChart = () => {
    if (!analytics?.monthlyStats || analytics.monthlyStats.length === 0) return null;

    const maxDuration = Math.max(...analytics.monthlyStats.map(stat => stat.totalDuration));
    const chartHeight = 120;

    return (
      <View style={styles.chartContainer}>
        <Text style={styles.chartTitle}>Monthly Study Time</Text>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          <View style={styles.barChart}>
            {analytics.monthlyStats.map((stat, index) => {
              const height = maxDuration > 0 ? (stat.totalDuration / maxDuration) * chartHeight : 0;
              const monthDate = new Date(stat.year, stat.month - 1);
              const currentMonth = new Date();
              const isCurrentMonth = monthDate.getMonth() === currentMonth.getMonth() && 
                                   monthDate.getFullYear() === currentMonth.getFullYear();
              
              return (
                <View key={`${stat.year}-${stat.month}`} style={styles.barContainer}>
                  <View style={[styles.barWrapper, { height: chartHeight }]}>
                    <LinearGradient
                      colors={stat.targetAchieved ? ['#10B981', '#059669'] : ['#6366F1', '#8B5CF6']}
                      style={[
                        styles.bar,
                        { 
                          height: Math.max(height, 4),
                          opacity: isCurrentMonth ? 1 : 0.8
                        }
                      ]}
                    />
                  </View>
                  <Text style={[styles.barLabel, isCurrentMonth && styles.barLabelCurrent]}>
                    {monthDate.toLocaleDateString('en-US', { 
                      month: 'short',
                      year: stat.year !== new Date().getFullYear() ? '2-digit' : undefined
                    })}
                  </Text>
                  <Text style={styles.barValue}>
                    {formatTime(stat.totalDuration)}
                  </Text>
                  {stat.targetAchieved && (
                    <MaterialIcons name="check-circle" size={12} color="#10B981" />
                  )}
                </View>
              );
            })}
          </View>
        </ScrollView>
      </View>
    );
  };

  const renderMonthlyList = () => {
    if (!analytics?.monthlyStats || analytics.monthlyStats.length === 0) return null;

    return (
      <View style={styles.listContainer}>
        <Text style={styles.listTitle}>Monthly Breakdown</Text>
        {analytics.monthlyStats.map((stat, index) => {
          const monthDate = new Date(stat.year, stat.month - 1);
          const currentMonth = new Date();
          const isCurrentMonth = monthDate.getMonth() === currentMonth.getMonth() && 
                                 monthDate.getFullYear() === currentMonth.getFullYear();
          
          const monthLabel = isCurrentMonth ? 'This Month' : 
            monthDate.toLocaleDateString('en-US', { month: 'long', year: 'numeric' });

          const averageDaily = stat.activeDays > 0 ? stat.totalDuration / stat.activeDays : 0;
          const consistencyPercentage = (stat.activeDays / new Date(stat.year, stat.month, 0).getDate()) * 100;

          return (
            <View key={`${stat.year}-${stat.month}`} style={styles.monthlyItem}>
              <View style={styles.monthlyHeader}>
                <View style={styles.monthlyDateContainer}>
                  <Text style={[styles.monthlyDate, isCurrentMonth && styles.monthlyDateCurrent]}>
                    {monthLabel}
                  </Text>
                  {stat.targetAchieved && (
                    <MaterialIcons name="check-circle" size={16} color="#10B981" />
                  )}
                </View>
                <Text style={styles.monthlyDuration}>
                  {formatTime(stat.totalDuration)}
                </Text>
              </View>
              
              <View style={styles.monthlyDetails}>
                <View style={styles.monthlyDetailItem}>
                  <MaterialIcons name="play-circle-outline" size={16} color="#64748B" />
                  <Text style={styles.monthlyDetailText}>
                    {stat.sessionCount} session{stat.sessionCount !== 1 ? 's' : ''}
                  </Text>
                </View>
                
                <View style={styles.monthlyDetailItem}>
                  <MaterialIcons name="calendar-today" size={16} color="#64748B" />
                  <Text style={styles.monthlyDetailText}>
                    {stat.activeDays} active day{stat.activeDays !== 1 ? 's' : ''}
                  </Text>
                </View>
                
                <View style={styles.monthlyDetailItem}>
                  <MaterialIcons name="trending-up" size={16} color="#64748B" />
                  <Text style={styles.monthlyDetailText}>
                    {formatTime(averageDaily)} avg/day
                  </Text>
                </View>
                
                {stat.completedPomodoros > 0 && (
                  <View style={styles.monthlyDetailItem}>
                    <MaterialIcons name="timer" size={16} color="#64748B" />
                    <Text style={styles.monthlyDetailText}>
                      {stat.completedPomodoros} pomodoro{stat.completedPomodoros !== 1 ? 's' : ''}
                    </Text>
                  </View>
                )}
                
                {stat.averageProductivityRating > 0 && (
                  <View style={styles.monthlyDetailItem}>
                    <MaterialIcons name="star" size={16} color="#F59E0B" />
                    <Text style={styles.monthlyDetailText}>
                      {stat.averageProductivityRating.toFixed(1)} avg rating
                    </Text>
                  </View>
                )}
              </View>

              {/* Consistency indicator */}
              <View style={styles.consistencyContainer}>
                <View style={styles.consistencyHeader}>
                  <Text style={styles.consistencyLabel}>Consistency</Text>
                  <Text style={styles.consistencyPercentage}>
                    {Math.round(consistencyPercentage)}%
                  </Text>
                </View>
                <View style={styles.consistencyBar}>
                  <LinearGradient
                    colors={['#6366F1', '#8B5CF6']}
                    style={[
                      styles.consistencyFill,
                      { width: `${Math.min(consistencyPercentage, 100)}%` }
                    ]}
                  />
                </View>
              </View>

              {/* Monthly goal progress */}
              <View style={styles.goalContainer}>
                <View style={styles.goalHeader}>
                  <Text style={styles.goalLabel}>Monthly Goal</Text>
                  <Text style={styles.goalPercentage}>
                    {Math.round((stat.totalDuration / (30 * 60 * 60)) * 100)}%
                  </Text>
                </View>
                <View style={styles.goalBar}>
                  <LinearGradient
                    colors={stat.targetAchieved ? ['#10B981', '#059669'] : ['#F59E0B', '#D97706']}
                    style={[
                      styles.goalFill,
                      { width: `${Math.min((stat.totalDuration / (30 * 60 * 60)) * 100, 100)}%` }
                    ]}
                  />
                </View>
                <Text style={styles.goalText}>
                  {formatTime(stat.totalDuration)} / 30h goal
                </Text>
              </View>
            </View>
          );
        })}
      </View>
    );
  };

  const renderMonthlySummary = () => {
    if (!analytics?.monthlyStats || analytics.monthlyStats.length === 0) return null;

    const totalTime = analytics.monthlyStats.reduce((sum, stat) => sum + stat.totalDuration, 0);
    const totalSessions = analytics.monthlyStats.reduce((sum, stat) => sum + stat.sessionCount, 0);
    const totalActiveDays = analytics.monthlyStats.reduce((sum, stat) => sum + stat.activeDays, 0);
    const monthsWithTarget = analytics.monthlyStats.filter(stat => stat.targetAchieved).length;
    const averageMonthlyTime = analytics.monthlyStats.length > 0 ? totalTime / analytics.monthlyStats.length : 0;

    return (
      <View style={styles.summaryContainer}>
        <Text style={styles.summaryTitle}>Monthly Summary</Text>
        <View style={styles.summaryGrid}>
          <View style={styles.summaryCard}>
            <Text style={styles.summaryValue}>{formatTime(totalTime)}</Text>
            <Text style={styles.summaryLabel}>Total Time</Text>
          </View>
          <View style={styles.summaryCard}>
            <Text style={styles.summaryValue}>{formatTime(averageMonthlyTime)}</Text>
            <Text style={styles.summaryLabel}>Avg/Month</Text>
          </View>
          <View style={styles.summaryCard}>
            <Text style={styles.summaryValue}>{totalSessions}</Text>
            <Text style={styles.summaryLabel}>Total Sessions</Text>
          </View>
          <View style={styles.summaryCard}>
            <Text style={styles.summaryValue}>{monthsWithTarget}</Text>
            <Text style={styles.summaryLabel}>Goals Met</Text>
          </View>
        </View>
      </View>
    );
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <MaterialIcons name="calendar-month" size={48} color="#6366F1" />
        <Text style={styles.loadingText}>Loading monthly analytics...</Text>
      </View>
    );
  }

  if (error) {
    return (
      <View style={styles.errorContainer}>
        <MaterialIcons name="error-outline" size={48} color="#EF4444" />
        <Text style={styles.errorText}>{error}</Text>
      </View>
    );
  }

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      {renderMonthlySummary()}
      {renderMonthlyChart()}
      {renderMonthlyList()}
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  summaryContainer: {
    margin: 20,
  },
  summaryTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1E293B',
    marginBottom: 16,
  },
  summaryGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  summaryCard: {
    flex: 1,
    minWidth: (width - 52) / 2,
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  summaryValue: {
    fontSize: 18,
    fontWeight: '700',
    color: '#1E293B',
    marginBottom: 4,
  },
  summaryLabel: {
    fontSize: 12,
    color: '#64748B',
  },
  chartContainer: {
    backgroundColor: '#FFFFFF',
    margin: 20,
    marginTop: 0,
    borderRadius: 16,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  chartTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1E293B',
    marginBottom: 16,
  },
  barChart: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    gap: 16,
    paddingBottom: 40,
  },
  barContainer: {
    alignItems: 'center',
    minWidth: 60,
  },
  barWrapper: {
    justifyContent: 'flex-end',
    marginBottom: 8,
  },
  bar: {
    width: 40,
    borderRadius: 20,
    minHeight: 4,
  },
  barLabel: {
    fontSize: 10,
    color: '#64748B',
    marginBottom: 2,
    textAlign: 'center',
  },
  barLabelCurrent: {
    fontWeight: '600',
    color: '#1E293B',
  },
  barValue: {
    fontSize: 9,
    color: '#64748B',
    textAlign: 'center',
  },
  listContainer: {
    margin: 20,
    marginTop: 0,
  },
  listTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1E293B',
    marginBottom: 16,
  },
  monthlyItem: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  monthlyHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  monthlyDateContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    flex: 1,
  },
  monthlyDate: {
    fontSize: 16,
    fontWeight: '500',
    color: '#1E293B',
  },
  monthlyDateCurrent: {
    color: '#6366F1',
    fontWeight: '600',
  },
  monthlyDuration: {
    fontSize: 16,
    fontWeight: '600',
    color: '#6366F1',
  },
  monthlyDetails: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 16,
    marginBottom: 16,
  },
  monthlyDetailItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  monthlyDetailText: {
    fontSize: 12,
    color: '#64748B',
  },
  consistencyContainer: {
    marginBottom: 12,
  },
  consistencyHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  consistencyLabel: {
    fontSize: 12,
    color: '#64748B',
  },
  consistencyPercentage: {
    fontSize: 12,
    fontWeight: '600',
    color: '#6366F1',
  },
  consistencyBar: {
    height: 4,
    backgroundColor: '#E2E8F0',
    borderRadius: 2,
    overflow: 'hidden',
  },
  consistencyFill: {
    height: '100%',
    borderRadius: 2,
  },
  goalContainer: {
    borderTopWidth: 1,
    borderTopColor: '#E2E8F0',
    paddingTop: 12,
  },
  goalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  goalLabel: {
    fontSize: 12,
    color: '#64748B',
  },
  goalPercentage: {
    fontSize: 12,
    fontWeight: '600',
    color: '#10B981',
  },
  goalBar: {
    height: 6,
    backgroundColor: '#E2E8F0',
    borderRadius: 3,
    overflow: 'hidden',
    marginBottom: 4,
  },
  goalFill: {
    height: '100%',
    borderRadius: 3,
  },
  goalText: {
    fontSize: 12,
    color: '#64748B',
    textAlign: 'center',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  loadingText: {
    fontSize: 16,
    color: '#64748B',
    marginTop: 12,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  errorText: {
    fontSize: 16,
    color: '#EF4444',
    marginTop: 12,
    textAlign: 'center',
  },
});
