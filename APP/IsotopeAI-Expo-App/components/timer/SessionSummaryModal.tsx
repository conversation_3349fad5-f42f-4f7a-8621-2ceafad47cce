import React, { useState } from 'react';
import {
  View,
  Text,
  Modal,
  TextInput,
  TouchableOpacity,
  ScrollView,
  StyleSheet,
  Dimensions,
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { SessionSummary, TaskType, TASK_TYPES } from '@/types/app';

interface SessionSummaryModalProps {
  visible: boolean;
  onClose: () => void;
  onSubmit: (summary: SessionSummary) => void;
  sessionDuration: number;
  subject?: string;
  initialTaskName?: string;
  initialTaskType?: TaskType;
}

const { width } = Dimensions.get('window');

export function SessionSummaryModal({
  visible,
  onClose,
  onSubmit,
  sessionDuration,
  subject,
  initialTaskName = '',
  initialTaskType = 'General Study'
}: SessionSummaryModalProps) {
  const [taskName, setTaskName] = useState(initialTaskName);
  const [taskType, setTaskType] = useState<TaskType>(initialTaskType);
  const [productivityRating, setProductivityRating] = useState<number>(0);
  const [notes, setNotes] = useState('');
  const [feedback, setFeedback] = useState('');

  const formatDuration = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    
    if (hours > 0) {
      return `${hours}h ${minutes}m`;
    }
    return `${minutes}m`;
  };

  const handleSubmit = () => {
    const summary: SessionSummary = {
      taskName: taskName.trim() || undefined,
      taskType,
      productivityRating: productivityRating > 0 ? productivityRating : undefined,
      notes: notes.trim() || undefined,
      feedback: feedback.trim() || undefined,
    };
    
    onSubmit(summary);
    
    // Reset form
    setTaskName('');
    setTaskType('General Study');
    setProductivityRating(0);
    setNotes('');
    setFeedback('');
  };

  const handleSkip = () => {
    onSubmit({});
    onClose();
  };

  const renderStarRating = () => {
    return (
      <View style={styles.starContainer}>
        {[1, 2, 3, 4, 5].map((star) => (
          <TouchableOpacity
            key={star}
            onPress={() => setProductivityRating(star)}
            style={styles.starButton}
          >
            <MaterialIcons
              name={star <= productivityRating ? 'star' : 'star-border'}
              size={32}
              color={star <= productivityRating ? '#FFD700' : '#E0E0E0'}
            />
          </TouchableOpacity>
        ))}
      </View>
    );
  };

  const renderTaskTypeSelector = () => {
    return (
      <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.taskTypeScroll}>
        {TASK_TYPES.map((type) => (
          <TouchableOpacity
            key={type}
            onPress={() => setTaskType(type)}
            style={[
              styles.taskTypeChip,
              taskType === type && styles.taskTypeChipSelected
            ]}
          >
            <Text style={[
              styles.taskTypeText,
              taskType === type && styles.taskTypeTextSelected
            ]}>
              {type}
            </Text>
          </TouchableOpacity>
        ))}
      </ScrollView>
    );
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <View style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <MaterialIcons name="close" size={24} color="#666" />
          </TouchableOpacity>
          <Text style={styles.title}>Session Complete! 🎉</Text>
          <View style={styles.placeholder} />
        </View>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {/* Session Summary */}
          <View style={styles.summaryCard}>
            <Text style={styles.summaryTitle}>Study Session Summary</Text>
            <View style={styles.summaryRow}>
              <Text style={styles.summaryLabel}>Duration:</Text>
              <Text style={styles.summaryValue}>{formatDuration(sessionDuration)}</Text>
            </View>
            {subject && (
              <View style={styles.summaryRow}>
                <Text style={styles.summaryLabel}>Subject:</Text>
                <Text style={styles.summaryValue}>{subject}</Text>
              </View>
            )}
          </View>

          {/* Task Name */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>What did you work on?</Text>
            <TextInput
              style={styles.textInput}
              placeholder="e.g., Chapter 5 exercises, Essay writing..."
              value={taskName}
              onChangeText={setTaskName}
              multiline
              maxLength={100}
            />
          </View>

          {/* Task Type */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Task Type</Text>
            {renderTaskTypeSelector()}
          </View>

          {/* Productivity Rating */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>How productive did you feel?</Text>
            <Text style={styles.sectionSubtitle}>Rate your focus and productivity</Text>
            {renderStarRating()}
            {productivityRating > 0 && (
              <Text style={styles.ratingText}>
                {productivityRating === 1 && "Not very productive"}
                {productivityRating === 2 && "Somewhat productive"}
                {productivityRating === 3 && "Moderately productive"}
                {productivityRating === 4 && "Very productive"}
                {productivityRating === 5 && "Extremely productive"}
              </Text>
            )}
          </View>

          {/* Notes */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Notes (Optional)</Text>
            <TextInput
              style={[styles.textInput, styles.notesInput]}
              placeholder="Any notes about this session..."
              value={notes}
              onChangeText={setNotes}
              multiline
              maxLength={200}
            />
          </View>

          {/* Feedback */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Feedback (Optional)</Text>
            <TextInput
              style={[styles.textInput, styles.notesInput]}
              placeholder="How did this session go? Any challenges or insights?"
              value={feedback}
              onChangeText={setFeedback}
              multiline
              maxLength={200}
            />
          </View>
        </ScrollView>

        {/* Action Buttons */}
        <View style={styles.actions}>
          <TouchableOpacity onPress={handleSkip} style={styles.skipButton}>
            <Text style={styles.skipButtonText}>Skip</Text>
          </TouchableOpacity>
          <TouchableOpacity onPress={handleSubmit} style={styles.submitButton}>
            <Text style={styles.submitButtonText}>Save Session</Text>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8FAFC',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingTop: 60,
    paddingBottom: 20,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E2E8F0',
  },
  closeButton: {
    padding: 8,
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
    color: '#1E293B',
  },
  placeholder: {
    width: 40,
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  summaryCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 20,
    marginTop: 20,
    marginBottom: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  summaryTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1E293B',
    marginBottom: 16,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  summaryLabel: {
    fontSize: 16,
    color: '#64748B',
  },
  summaryValue: {
    fontSize: 16,
    fontWeight: '500',
    color: '#1E293B',
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1E293B',
    marginBottom: 8,
  },
  sectionSubtitle: {
    fontSize: 14,
    color: '#64748B',
    marginBottom: 16,
  },
  textInput: {
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    padding: 16,
    fontSize: 16,
    color: '#1E293B',
    borderWidth: 1,
    borderColor: '#E2E8F0',
    minHeight: 48,
  },
  notesInput: {
    minHeight: 80,
    textAlignVertical: 'top',
  },
  taskTypeScroll: {
    marginBottom: 8,
  },
  taskTypeChip: {
    backgroundColor: '#FFFFFF',
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginRight: 8,
    borderWidth: 1,
    borderColor: '#E2E8F0',
  },
  taskTypeChipSelected: {
    backgroundColor: '#6366F1',
    borderColor: '#6366F1',
  },
  taskTypeText: {
    fontSize: 14,
    color: '#64748B',
    fontWeight: '500',
  },
  taskTypeTextSelected: {
    color: '#FFFFFF',
  },
  starContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginBottom: 12,
  },
  starButton: {
    padding: 4,
    marginHorizontal: 4,
  },
  ratingText: {
    textAlign: 'center',
    fontSize: 14,
    color: '#64748B',
    fontStyle: 'italic',
  },
  actions: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingVertical: 20,
    backgroundColor: '#FFFFFF',
    borderTopWidth: 1,
    borderTopColor: '#E2E8F0',
    gap: 12,
  },
  skipButton: {
    flex: 1,
    backgroundColor: '#F1F5F9',
    borderRadius: 8,
    paddingVertical: 16,
    alignItems: 'center',
  },
  skipButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#64748B',
  },
  submitButton: {
    flex: 2,
    backgroundColor: '#6366F1',
    borderRadius: 8,
    paddingVertical: 16,
    alignItems: 'center',
  },
  submitButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
  },
});
