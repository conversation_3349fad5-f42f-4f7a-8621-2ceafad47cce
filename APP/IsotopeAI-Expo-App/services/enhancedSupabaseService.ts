import { supabase } from '@/lib/supabase';
import { 
  Subject, 
  TimerSession, 
  UserProfile,
  DailyStat,
  WeeklyStat,
  MonthlyStat,
  SubjectStat,
  TaskTypeStat,
  Analytics,
  StreakInfo,
  ApiResponse,
  AppError,
  RealtimeSubscription,
  DateRange
} from '@/types/app';

export class EnhancedSupabaseService {
  private subscriptions: Map<string, any> = new Map();

  // Error handling helper
  private handleError(error: any, operation: string): AppError {
    console.error(`Error in ${operation}:`, error);
    return {
      code: error.code || 'UNKNOWN_ERROR',
      message: error.message || `Failed to ${operation}`,
      details: error,
      timestamp: new Date()
    };
  }

  // Success response helper
  private successResponse<T>(data: T): ApiResponse<T> {
    return { data, success: true };
  }

  // Error response helper
  private errorResponse(error: AppError): ApiResponse<never> {
    return { error, success: false };
  }

  // Subject Management with enhanced error handling
  async getSubjects(userId: string): Promise<ApiResponse<Subject[]>> {
    try {
      const { data, error } = await supabase
        .from('userSubjects')
        .select('*')
        .eq('userId', userId)
        .order('createdAt', { ascending: true });

      if (error) throw error;
      
      const subjects = data?.map(subject => ({
        id: subject.id,
        name: subject.name,
        color: subject.color || '#3B82F6',
        createdAt: new Date(subject.createdAt)
      })) || [];

      return this.successResponse(subjects);
    } catch (error) {
      return this.errorResponse(this.handleError(error, 'fetch subjects'));
    }
  }

  async createSubject(userId: string, name: string, color: string): Promise<ApiResponse<Subject>> {
    try {
      const newSubject = {
        id: `subject_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        userId,
        name,
        color,
        createdAt: new Date().toISOString()
      };

      const { data, error } = await supabase
        .from('userSubjects')
        .insert([newSubject])
        .select()
        .single();

      if (error) throw error;

      const subject = {
        id: data.id,
        name: data.name,
        color: data.color,
        createdAt: new Date(data.createdAt)
      };

      return this.successResponse(subject);
    } catch (error) {
      return this.errorResponse(this.handleError(error, 'create subject'));
    }
  }

  async updateSubject(subjectId: string, updates: Partial<Pick<Subject, 'name' | 'color'>>): Promise<ApiResponse<Subject>> {
    try {
      const { data, error } = await supabase
        .from('userSubjects')
        .update(updates)
        .eq('id', subjectId)
        .select()
        .single();

      if (error) throw error;

      const subject = {
        id: data.id,
        name: data.name,
        color: data.color,
        createdAt: new Date(data.createdAt)
      };

      return this.successResponse(subject);
    } catch (error) {
      return this.errorResponse(this.handleError(error, 'update subject'));
    }
  }

  async deleteSubject(subjectId: string): Promise<ApiResponse<void>> {
    try {
      const { error } = await supabase
        .from('userSubjects')
        .delete()
        .eq('id', subjectId);

      if (error) throw error;
      return this.successResponse(undefined);
    } catch (error) {
      return this.errorResponse(this.handleError(error, 'delete subject'));
    }
  }

  // Enhanced Study Session Management
  async getStudySessions(userId: string, dateRange?: DateRange): Promise<ApiResponse<TimerSession[]>> {
    try {
      let query = supabase
        .from('study_sessions')
        .select('*')
        .eq('user_id', userId);

      if (dateRange) {
        query = query
          .gte('start_time', dateRange.start.toISOString())
          .lte('start_time', dateRange.end.toISOString());
      }

      const { data, error } = await query.order('start_time', { ascending: false });

      if (error) throw error;

      const sessions = (data || []).map((session: any) => ({
        id: session.id,
        startTime: new Date(session.start_time),
        endTime: session.end_time ? new Date(session.end_time) : null,
        duration: session.duration,
        subject: session.subject || undefined,
        subjectColor: session.subject_color || undefined,
        mode: session.mode as 'stopwatch' | 'pomodoro' || 'stopwatch',
        phase: session.phase as 'work' | 'shortBreak' | 'longBreak' || undefined,
        completed: session.completed || false,
        notes: session.notes || undefined,
        taskName: session.task_name || undefined,
        taskType: session.task_type || undefined,
        productivityRating: session.productivity_rating || undefined,
        feedback: session.feedback || undefined,
        date: session.date,
      }));

      return this.successResponse(sessions);
    } catch (error) {
      return this.errorResponse(this.handleError(error, 'fetch study sessions'));
    }
  }

  async createStudySession(userId: string, session: Omit<TimerSession, 'id'>): Promise<ApiResponse<TimerSession>> {
    try {
      const sessionData = {
        id: `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        user_id: userId,
        start_time: session.startTime.toISOString(),
        end_time: session.endTime?.toISOString() || null,
        duration: session.duration,
        subject: session.subject || null,
        subject_color: session.subjectColor || null,
        mode: session.mode,
        phase: session.phase || null,
        completed: session.completed,
        notes: session.notes || null,
        task_name: session.taskName || null,
        task_type: session.taskType || null,
        productivity_rating: session.productivityRating || null,
        feedback: session.feedback || null,
        date: session.date,
        created_at: new Date().toISOString(),
      };

      const { data, error } = await supabase
        .from('study_sessions')
        .insert(sessionData)
        .select()
        .single();

      if (error) throw error;

      const createdSession = {
        id: data.id,
        startTime: new Date(data.start_time),
        endTime: data.end_time ? new Date(data.end_time) : null,
        duration: data.duration,
        subject: data.subject || undefined,
        subjectColor: data.subject_color || undefined,
        mode: data.mode as 'stopwatch' | 'pomodoro',
        phase: data.phase as 'work' | 'shortBreak' | 'longBreak' || undefined,
        completed: data.completed,
        notes: data.notes || undefined,
        taskName: data.task_name || undefined,
        taskType: data.task_type || undefined,
        productivityRating: data.productivity_rating || undefined,
        feedback: data.feedback || undefined,
        date: data.date,
      };

      return this.successResponse(createdSession);
    } catch (error) {
      return this.errorResponse(this.handleError(error, 'create study session'));
    }
  }

  async updateStudySession(sessionId: string, updates: Partial<TimerSession>): Promise<ApiResponse<TimerSession>> {
    try {
      const updateData: any = {};
      
      if (updates.endTime !== undefined) updateData.end_time = updates.endTime?.toISOString() || null;
      if (updates.duration !== undefined) updateData.duration = updates.duration;
      if (updates.completed !== undefined) updateData.completed = updates.completed;
      if (updates.notes !== undefined) updateData.notes = updates.notes || null;
      if (updates.productivityRating !== undefined) updateData.productivity_rating = updates.productivityRating || null;
      if (updates.feedback !== undefined) updateData.feedback = updates.feedback || null;

      const { data, error } = await supabase
        .from('study_sessions')
        .update(updateData)
        .eq('id', sessionId)
        .select()
        .single();

      if (error) throw error;

      const updatedSession = {
        id: data.id,
        startTime: new Date(data.start_time),
        endTime: data.end_time ? new Date(data.end_time) : null,
        duration: data.duration,
        subject: data.subject || undefined,
        subjectColor: data.subject_color || undefined,
        mode: data.mode as 'stopwatch' | 'pomodoro',
        phase: data.phase as 'work' | 'shortBreak' | 'longBreak' || undefined,
        completed: data.completed,
        notes: data.notes || undefined,
        taskName: data.task_name || undefined,
        taskType: data.task_type || undefined,
        productivityRating: data.productivity_rating || undefined,
        feedback: data.feedback || undefined,
        date: data.date,
      };

      return this.successResponse(updatedSession);
    } catch (error) {
      return this.errorResponse(this.handleError(error, 'update study session'));
    }
  }
}

  // User Profile Management
  async getUserProfile(userId: string): Promise<ApiResponse<UserProfile>> {
    try {
      const { data, error } = await supabase
        .from('users')
        .select('*')
        .eq('id', userId)
        .single();

      if (error) throw error;

      const profile: UserProfile = {
        id: data.id,
        email: data.email,
        username: data.username,
        display_name: data.display_name,
        photo_url: data.photo_url,
        daily_target: data.daily_target,
        daily_motivation: data.daily_motivation,
        day_start_time: data.day_start_time,
        created_at: new Date(data.created_at),
        updated_at: new Date(data.updated_at)
      };

      return this.successResponse(profile);
    } catch (error) {
      return this.errorResponse(this.handleError(error, 'fetch user profile'));
    }
  }

  async updateUserProfile(userId: string, updates: Partial<UserProfile>): Promise<ApiResponse<UserProfile>> {
    try {
      const updateData: any = {
        ...updates,
        updated_at: new Date().toISOString(),
      };

      const { data, error } = await supabase
        .from('users')
        .update(updateData)
        .eq('id', userId)
        .select()
        .single();

      if (error) throw error;

      const profile: UserProfile = {
        id: data.id,
        email: data.email,
        username: data.username,
        display_name: data.display_name,
        photo_url: data.photo_url,
        daily_target: data.daily_target,
        daily_motivation: data.daily_motivation,
        day_start_time: data.day_start_time,
        created_at: new Date(data.created_at),
        updated_at: new Date(data.updated_at)
      };

      return this.successResponse(profile);
    } catch (error) {
      return this.errorResponse(this.handleError(error, 'update user profile'));
    }
  }

  // Analytics Data Processing
  async getDailyStats(userId: string, dateRange: DateRange): Promise<ApiResponse<DailyStat[]>> {
    try {
      const sessionsResponse = await this.getStudySessions(userId, dateRange);
      if (!sessionsResponse.success || !sessionsResponse.data) {
        return this.errorResponse(sessionsResponse.error!);
      }

      const sessions = sessionsResponse.data;
      const dailyStatsMap = new Map<string, DailyStat>();

      // Initialize daily stats for each day in range
      const currentDate = new Date(dateRange.start);
      while (currentDate <= dateRange.end) {
        const dateStr = currentDate.toISOString().split('T')[0];
        dailyStatsMap.set(dateStr, {
          date: dateStr,
          totalDuration: 0,
          targetAchieved: false,
          sessionCount: 0,
          completedPomodoros: 0,
          subjectDurations: {},
          taskTypeDurations: {},
          averageProductivityRating: 0,
          streakDay: false
        });
        currentDate.setDate(currentDate.getDate() + 1);
      }

      // Process sessions
      let totalRatings = 0;
      let ratingCount = 0;

      sessions.forEach(session => {
        const dateStr = session.date;
        const stat = dailyStatsMap.get(dateStr);
        if (!stat) return;

        stat.totalDuration += session.duration;
        stat.sessionCount += 1;

        if (session.mode === 'pomodoro' && session.completed) {
          stat.completedPomodoros += 1;
        }

        if (session.subject) {
          stat.subjectDurations[session.subject] = (stat.subjectDurations[session.subject] || 0) + session.duration;
        }

        if (session.taskType) {
          stat.taskTypeDurations[session.taskType] = (stat.taskTypeDurations[session.taskType] || 0) + session.duration;
        }

        if (session.productivityRating) {
          totalRatings += session.productivityRating;
          ratingCount += 1;
        }
      });

      // Calculate averages and targets
      const userProfileResponse = await this.getUserProfile(userId);
      const dailyTarget = userProfileResponse.data?.daily_target || 60; // Default 1 hour

      Array.from(dailyStatsMap.values()).forEach(stat => {
        stat.targetAchieved = stat.totalDuration >= (dailyTarget * 60); // Convert minutes to seconds
        stat.averageProductivityRating = ratingCount > 0 ? totalRatings / ratingCount : 0;
        stat.streakDay = stat.totalDuration > 0;
      });

      return this.successResponse(Array.from(dailyStatsMap.values()));
    } catch (error) {
      return this.errorResponse(this.handleError(error, 'calculate daily stats'));
    }
  }

  async getStreakInfo(userId: string): Promise<ApiResponse<StreakInfo>> {
    try {
      // Get last 365 days of sessions to calculate streak
      const endDate = new Date();
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - 365);

      const dailyStatsResponse = await this.getDailyStats(userId, { start: startDate, end: endDate });
      if (!dailyStatsResponse.success || !dailyStatsResponse.data) {
        return this.errorResponse(dailyStatsResponse.error!);
      }

      const dailyStats = dailyStatsResponse.data.sort((a, b) => a.date.localeCompare(b.date));

      let currentStreak = 0;
      let longestStreak = 0;
      let tempStreak = 0;
      let streakStartDate = '';
      let isActiveToday = false;

      const today = new Date().toISOString().split('T')[0];

      // Calculate streaks
      for (let i = dailyStats.length - 1; i >= 0; i--) {
        const stat = dailyStats[i];

        if (stat.streakDay) {
          tempStreak += 1;
          if (tempStreak > longestStreak) {
            longestStreak = tempStreak;
          }

          // Current streak calculation (from today backwards)
          if (i === dailyStats.length - 1 || (currentStreak === 0 && stat.date === today)) {
            currentStreak = tempStreak;
            streakStartDate = stat.date;
            if (stat.date === today) {
              isActiveToday = true;
            }
          }
        } else {
          tempStreak = 0;
          if (currentStreak === 0) {
            currentStreak = 0;
          }
        }
      }

      const streakInfo: StreakInfo = {
        currentStreak,
        longestStreak,
        streakStartDate,
        isActiveToday
      };

      return this.successResponse(streakInfo);
    } catch (error) {
      return this.errorResponse(this.handleError(error, 'calculate streak info'));
    }
  }
}

  // Real-time Subscriptions
  async subscribeToStudySessions(userId: string, callback: (sessions: TimerSession[]) => void): Promise<string> {
    try {
      const channelName = `study_sessions_${userId}`;

      const channel = supabase
        .channel(channelName)
        .on(
          'postgres_changes',
          {
            event: '*',
            schema: 'public',
            table: 'study_sessions',
            filter: `user_id=eq.${userId}`,
          },
          async () => {
            // Refetch sessions when changes occur
            const response = await this.getStudySessions(userId);
            if (response.success && response.data) {
              callback(response.data);
            }
          }
        )
        .subscribe();

      this.subscriptions.set(channelName, channel);
      return channelName;
    } catch (error) {
      console.error('Error subscribing to study sessions:', error);
      throw error;
    }
  }

  async subscribeToSubjects(userId: string, callback: (subjects: Subject[]) => void): Promise<string> {
    try {
      const channelName = `subjects_${userId}`;

      const channel = supabase
        .channel(channelName)
        .on(
          'postgres_changes',
          {
            event: '*',
            schema: 'public',
            table: 'userSubjects',
            filter: `userId=eq.${userId}`,
          },
          async () => {
            // Refetch subjects when changes occur
            const response = await this.getSubjects(userId);
            if (response.success && response.data) {
              callback(response.data);
            }
          }
        )
        .subscribe();

      this.subscriptions.set(channelName, channel);
      return channelName;
    } catch (error) {
      console.error('Error subscribing to subjects:', error);
      throw error;
    }
  }

  async unsubscribe(channelName: string): Promise<void> {
    try {
      const channel = this.subscriptions.get(channelName);
      if (channel) {
        await supabase.removeChannel(channel);
        this.subscriptions.delete(channelName);
      }
    } catch (error) {
      console.error('Error unsubscribing from channel:', error);
    }
  }

  async unsubscribeAll(): Promise<void> {
    try {
      for (const [channelName, channel] of this.subscriptions) {
        await supabase.removeChannel(channel);
      }
      this.subscriptions.clear();
    } catch (error) {
      console.error('Error unsubscribing from all channels:', error);
    }
  }

  // Utility Methods
  async getSessionsByDateRange(userId: string, startDate: Date, endDate: Date): Promise<ApiResponse<TimerSession[]>> {
    return this.getStudySessions(userId, { start: startDate, end: endDate });
  }

  async getSessionsBySubject(userId: string, subject: string): Promise<ApiResponse<TimerSession[]>> {
    try {
      const { data, error } = await supabase
        .from('study_sessions')
        .select('*')
        .eq('user_id', userId)
        .eq('subject', subject)
        .order('start_time', { ascending: false });

      if (error) throw error;

      const sessions = (data || []).map((session: any) => ({
        id: session.id,
        startTime: new Date(session.start_time),
        endTime: session.end_time ? new Date(session.end_time) : null,
        duration: session.duration,
        subject: session.subject || undefined,
        subjectColor: session.subject_color || undefined,
        mode: session.mode as 'stopwatch' | 'pomodoro' || 'stopwatch',
        phase: session.phase as 'work' | 'shortBreak' | 'longBreak' || undefined,
        completed: session.completed || false,
        notes: session.notes || undefined,
        taskName: session.task_name || undefined,
        taskType: session.task_type || undefined,
        productivityRating: session.productivity_rating || undefined,
        feedback: session.feedback || undefined,
        date: session.date,
      }));

      return this.successResponse(sessions);
    } catch (error) {
      return this.errorResponse(this.handleError(error, 'fetch sessions by subject'));
    }
  }

  async getTotalStudyTime(userId: string): Promise<ApiResponse<number>> {
    try {
      const { data, error } = await supabase
        .from('study_sessions')
        .select('duration')
        .eq('user_id', userId);

      if (error) throw error;

      const totalSeconds = (data || []).reduce((total, session) => total + (session.duration || 0), 0);
      return this.successResponse(totalSeconds);
    } catch (error) {
      return this.errorResponse(this.handleError(error, 'calculate total study time'));
    }
  }

  async getSessionCount(userId: string): Promise<ApiResponse<number>> {
    try {
      const { count, error } = await supabase
        .from('study_sessions')
        .select('*', { count: 'exact', head: true })
        .eq('user_id', userId);

      if (error) throw error;

      return this.successResponse(count || 0);
    } catch (error) {
      return this.errorResponse(this.handleError(error, 'get session count'));
    }
  }

  // Batch operations for offline sync
  async batchCreateSessions(userId: string, sessions: Omit<TimerSession, 'id'>[]): Promise<ApiResponse<TimerSession[]>> {
    try {
      const sessionData = sessions.map(session => ({
        id: `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        user_id: userId,
        start_time: session.startTime.toISOString(),
        end_time: session.endTime?.toISOString() || null,
        duration: session.duration,
        subject: session.subject || null,
        subject_color: session.subjectColor || null,
        mode: session.mode,
        phase: session.phase || null,
        completed: session.completed,
        notes: session.notes || null,
        task_name: session.taskName || null,
        task_type: session.taskType || null,
        productivity_rating: session.productivityRating || null,
        feedback: session.feedback || null,
        date: session.date,
        created_at: new Date().toISOString(),
      }));

      const { data, error } = await supabase
        .from('study_sessions')
        .insert(sessionData)
        .select();

      if (error) throw error;

      const createdSessions = (data || []).map((session: any) => ({
        id: session.id,
        startTime: new Date(session.start_time),
        endTime: session.end_time ? new Date(session.end_time) : null,
        duration: session.duration,
        subject: session.subject || undefined,
        subjectColor: session.subject_color || undefined,
        mode: session.mode as 'stopwatch' | 'pomodoro',
        phase: session.phase as 'work' | 'shortBreak' | 'longBreak' || undefined,
        completed: session.completed,
        notes: session.notes || undefined,
        taskName: session.task_name || undefined,
        taskType: session.task_type || undefined,
        productivityRating: session.productivity_rating || undefined,
        feedback: session.feedback || undefined,
        date: session.date,
      }));

      return this.successResponse(createdSessions);
    } catch (error) {
      return this.errorResponse(this.handleError(error, 'batch create sessions'));
    }
  }
}

export const enhancedSupabaseService = new EnhancedSupabaseService();
