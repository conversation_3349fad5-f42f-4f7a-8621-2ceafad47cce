import { useState, useEffect } from 'react';
import { Subject } from '@/types/app';
import { Platform } from 'react-native';
import { storageService } from '@/services/storageService';
import { supabaseService } from '@/services/supabaseService';
import { useAuth } from '@/contexts/AuthContext';

const STORAGE_KEY = 'isotope_subjects';

export function useSubjects() {
  const { user } = useAuth();
  const [subjects, setSubjects] = useState<Subject[]>([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (user) {
      loadSubjects();
    }
  }, [user]);

  const loadSubjects = async () => {
    if (!user) return;

    try {
      setLoading(true);
      // Try to load from Supabase first
      const supabaseSubjects = await supabaseService.getUserSubjects(user.id);
      if (supabaseSubjects.length > 0) {
        setSubjects(supabaseSubjects);
      } else {
        // Fallback to local storage for migration
        const stored = await storageService.getObject<any[]>(STORAGE_KEY);
        if (stored) {
          const localSubjects = stored.map((s: any) => ({
            ...s,
            createdAt: new Date(s.createdAt),
          }));
          setSubjects(localSubjects);

          // Migrate local subjects to Supabase
          for (const subject of localSubjects) {
            await supabaseService.createUserSubject(user.id, subject);
          }
        }
      }
    } catch (error) {
      console.error('Error loading subjects:', error);
      // Fallback to local storage on error
      try {
        const stored = await storageService.getObject<any[]>(STORAGE_KEY);
        if (stored) {
          setSubjects(stored.map((s: any) => ({
            ...s,
            createdAt: new Date(s.createdAt),
          })));
        }
      } catch (localError) {
        console.error('Error loading local subjects:', localError);
      }
    } finally {
      setLoading(false);
    }
  };

  const saveSubjects = async (newSubjects: Subject[]) => {
    try {
      // Save to local storage for offline support
      await storageService.setObject(STORAGE_KEY, newSubjects);
      setSubjects(newSubjects);
    } catch (error) {
      console.error('Error saving subjects:', error);
    }
  };

  const addSubject = async (subjectData: Omit<Subject, 'id' | 'createdAt'>) => {
    if (!user) return;

    const newSubject: Subject = {
      id: Date.now().toString(),
      ...subjectData,
      createdAt: new Date(),
    };

    try {
      // Save to Supabase
      const supabaseSubject = await supabaseService.createUserSubject(user.id, newSubject);
      if (supabaseSubject) {
        const newSubjects = [...subjects, supabaseSubject];
        saveSubjects(newSubjects);
      }
    } catch (error) {
      console.error('Error adding subject to Supabase:', error);
      // Fallback to local only
      const newSubjects = [...subjects, newSubject];
      saveSubjects(newSubjects);
    }
  };

  const updateSubject = async (id: string, updates: Partial<Subject>) => {
    try {
      // Update in Supabase
      await supabaseService.updateUserSubject(id, updates);

      // Update local state
      const newSubjects = subjects.map(subject =>
        subject.id === id ? { ...subject, ...updates } : subject
      );
      saveSubjects(newSubjects);
    } catch (error) {
      console.error('Error updating subject:', error);
      // Fallback to local only
      const newSubjects = subjects.map(subject =>
        subject.id === id ? { ...subject, ...updates } : subject
      );
      saveSubjects(newSubjects);
    }
  };

  const deleteSubject = async (id: string) => {
    try {
      // Delete from Supabase
      await supabaseService.deleteUserSubject(id);

      // Update local state
      const newSubjects = subjects.filter(subject => subject.id !== id);
      saveSubjects(newSubjects);
    } catch (error) {
      console.error('Error deleting subject:', error);
      // Fallback to local only
      const newSubjects = subjects.filter(subject => subject.id !== id);
      saveSubjects(newSubjects);
    }
  };

  const getSubjectById = (id: string) => {
    return subjects.find(subject => subject.id === id);
  };

  return {
    subjects,
    loading,
    addSubject,
    updateSubject,
    deleteSubject,
    getSubjectById,
  };
}